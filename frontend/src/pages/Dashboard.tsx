import React, { useEffect, useState, useMemo } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { useAuth, useDeployment } from '../contexts';
import { Deployment } from '../api/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { apiClient } from '@/api';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, ExternalLink } from 'lucide-react';
import Modal from '@/components/ui/modal';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const {
    deployments,
    isLoading,
    error,
    fetchDeployments,
    silentUpdateDeployments,
    deleteDeployment,
    clearError,
  } = useDeployment();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [detailOpen, setDetailOpen] = useState<number | null>(null);
  const [failureReasonExpanded, setFailureReasonExpanded] = useState(false);
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [userFilter, setUserFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deploymentToDelete, setDeploymentToDelete] = useState<Deployment | null>(null);

  // Copy to clipboard function
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      setTimeout(() => setCopiedText(null), 2000); // Clear after 2 seconds
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // Debounce search query to improve performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Check if we have processing deployments for the polling indicator
  const hasProcessingDeployments = useMemo(() => {
    const processingStates = ['CREATING', 'PROVISIONING', 'DNS_VALIDATION', 'RESTARTING', 'DESTROYING'];
    return deployments.some(deployment => processingStates.includes(deployment.status));
  }, [deployments]);

  useEffect(() => {
    if (user) {
      // Let the backend handle role-based filtering
      fetchDeployments();
    }
  }, [user]); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-refresh deployments when there are processing deployments
  useEffect(() => {
    if (hasProcessingDeployments) {
      const interval = setInterval(() => {
        // Use silent update that only updates if there are changes
        silentUpdateDeployments();
      }, 3000); // Poll every 3 seconds

      return () => clearInterval(interval);
    }
  }, [hasProcessingDeployments, silentUpdateDeployments]);

  // Handle URL parameters
  useEffect(() => {
    const deploymentId = searchParams.get('deployment');
    const userParam = searchParams.get('user');

    // Handle deployment modal
    if (deploymentId && deployments.length > 0) {
      const deploymentIdNum = parseInt(deploymentId, 10);
      const deployment = deployments.find(d => d.id === deploymentIdNum);
      if (deployment) {
        setDetailOpen(deploymentIdNum);
        setFailureReasonExpanded(false);
        setCopiedText(null);
        // Clear the URL parameter after opening the modal
        setSearchParams(prev => {
          const newParams = new URLSearchParams(prev);
          newParams.delete('deployment');
          return newParams;
        });
      }
    }

    // Handle user filter - sync with URL parameter
    if (userParam !== userFilter) {
      setUserFilter(userParam || '');
    }
  }, [deployments, searchParams, setSearchParams, userFilter]);

  // Handle user filter changes and update URL
  const handleUserFilterChange = (value: string) => {
    setUserFilter(value);

    // Update URL parameters
    setSearchParams(prev => {
      const newParams = new URLSearchParams(prev);
      if (value.trim()) {
        newParams.set('user', value.trim());
      } else {
        newParams.delete('user');
      }
      return newParams;
    });
  };

  // Clear user filter
  const clearUserFilter = () => {
    handleUserFilterChange('');
  };

  const handleDeleteDeployment = (deployment: Deployment) => {
    setDeploymentToDelete(deployment);
    setShowDeleteModal(true);
  };

  const handleRetryDnsValidation = async (deployment: Deployment) => {
    try {
      setDeletingId(deployment.id); // Reuse the loading state
      await apiClient.retryDnsValidation(deployment.id);

      // Show success message and refresh deployments data
      alert('DNS validation retry initiated. The deployment status will update shortly.');

      // Refresh deployments data without reloading the page
      setTimeout(() => {
        fetchDeployments(); // Use the context function to refresh data
      }, 2000);

    } catch (error) {
      console.error('Error retrying DNS validation:', error);
      alert('Failed to retry DNS validation. Please try again.');
    } finally {
      setDeletingId(null);
    }
  };

  const handleRestartDeployment = async (deployment: Deployment) => {
    try {
      setDeletingId(deployment.id); // Reuse the loading state
      await apiClient.restartDeployment(deployment.id);

      // Show success message and refresh deployments data
      alert('Deployment restart initiated. This will restart the stack to fix SSL certificate issues.');

      // Refresh deployments data without reloading the page
      setTimeout(() => {
        fetchDeployments(); // Use the context function to refresh data
      }, 3000); // Give it a bit more time for restart

    } catch (error) {
      console.error('Error restarting deployment:', error);
      alert('Failed to restart deployment. Please try again.');
    } finally {
      setDeletingId(null);
    }
  };

  const confirmDeleteDeployment = async () => {
    if (!deploymentToDelete) return;

    setDeletingId(deploymentToDelete.id);
    try {
      await deleteDeployment(deploymentToDelete.id);
      setShowDeleteModal(false);
      setDeploymentToDelete(null);
    } catch (error) {
      // Error handled by context
    } finally {
      setDeletingId(null);
    }
  };

  const formatCost = (cost: number) => {
    // Convert from cents to dollars
    const costInDollars = cost / 100;
    return `${costInDollars.toFixed(2)}`;
  };

  const getStatusText = (deployment: any) => {
    if (deployment.deleted_at) {
      return 'Disabled';
    }
    switch (deployment.status) {
      case 'CREATING':
        return 'Creating...';
      case 'PROVISIONING':
        return 'Provisioning...';
      case 'ACTIVE':
        return 'Ready'; // Simplified: ACTIVE means ready to use
      case 'DNS_VALIDATION':
        return 'Validating DNS...';
      case 'READY':
        return 'Ready'; // Both ACTIVE and READY show as "Ready"
      case 'DNS_FAILED':
        return 'DNS Setup Required';
      case 'RESTARTING':
        return 'Restarting...';
      case 'DESTROYING':
        return 'Deactivating...';
      case 'FAILED':
        return 'Failed';
      case 'DESTROYED':
        return 'Destroyed';
      case 'DELETED':
        return 'Disabled';
      default:
        return deployment.status || 'Unknown';
    }
  };

  // Determine what actions are allowed for each deployment state
  const getAvailableActions = (deployment: any) => {
    if (deployment.deleted_at) {
      return { canDelete: true, canRestart: false, canRetryDns: false };
    }

    const isProcessing = ['CREATING', 'PROVISIONING', 'DNS_VALIDATION', 'RESTARTING', 'DESTROYING'].includes(deployment.status);

    switch (deployment.status) {
      case 'CREATING':
      case 'PROVISIONING':
      case 'DNS_VALIDATION':
      case 'RESTARTING':
      case 'DESTROYING':
        // No actions allowed during processing
        return { canDelete: false, canRestart: false, canRetryDns: false };

      case 'ACTIVE':
      case 'READY':
        // Fully operational - can restart or deactivate
        return { canDelete: true, canRestart: true, canRetryDns: false };

      case 'DNS_FAILED':
        // DNS issues - can retry DNS validation or deactivate
        return { canDelete: true, canRestart: false, canRetryDns: true };

      case 'FAILED':
        // Failed deployment - can only delete
        return { canDelete: true, canRestart: false, canRetryDns: false };

      default:
        // Unknown state - be conservative
        return { canDelete: false, canRestart: false, canRetryDns: false };
    }
  };

  const getAccessUrl = (deployment: Deployment) => {
    if (!deployment.domain) return null;

    // For Coolify and Coolify+ packages, use admin_subdomain.domain
    if (deployment.package === 'Coolify' || deployment.package === 'Coolify+') {
      const adminSubdomain = deployment.admin_subdomain || 'coolify';
      return `https://${adminSubdomain}.${deployment.domain}`;
    }
    // For Pangolin package, use admin_subdomain.domain
    if (deployment.package === 'Pangolin' && deployment.admin_subdomain) {
      return `https://${deployment.admin_subdomain}.${deployment.domain}`;
    }

    // For Pangolin+ and Pangolin+AI packages, use static_page_subdomain.domain
    if ((deployment.package === 'Pangolin+' || deployment.package === 'Pangolin+AI') && deployment.static_page_subdomain) {
      return `https://${deployment.static_page_subdomain}.${deployment.domain}`;
    }

    // Fallback to just the domain
    return `https://${deployment.domain}`;
  };

  const filteredDeployments = useMemo(() => {
    return deployments.filter(deployment => {
      // User filter (admin only) - search both username and email
      if (userFilter && user?.is_admin) {
        const filterLower = userFilter.toLowerCase();
        const usernameMatches = deployment.user?.username?.toLowerCase().includes(filterLower);
        const emailMatches = deployment.user?.email?.toLowerCase().includes(filterLower);
        if (!usernameMatches && !emailMatches) return false;
      }

      // Search query filter - use debounced search query
      if (!debouncedSearchQuery) return true;

      const searchLower = debouncedSearchQuery.toLowerCase();
      return (
        (deployment.client_name?.toLowerCase().includes(searchLower)) ||
        (deployment.package?.toLowerCase().includes(searchLower)) ||
        (deployment.cloud_provider?.toLowerCase().includes(searchLower)) ||
        (deployment.domain?.toLowerCase().includes(searchLower)) ||
        (deployment.user?.username?.toLowerCase().includes(searchLower)) ||
        (deployment.user?.email?.toLowerCase().includes(searchLower))
      );
    });
  }, [deployments, debouncedSearchQuery, userFilter, user?.is_admin]);

  // Pagination logic
  const { totalPages, paginatedDeployments } = useMemo(() => {
    const totalPages = Math.ceil(filteredDeployments.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedDeployments = filteredDeployments.slice(startIndex, endIndex);

    return { totalPages, paginatedDeployments };
  }, [filteredDeployments, currentPage, itemsPerPage]);

  // Reset to page 1 when search or filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchQuery, userFilter]);

  return (
    <div className="space-y-6">
      {/* Verification banner */}
      {user && user.is_verified === false && (
        <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200 text-yellow-900">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Please verify your email to enable deployments and account changes.</p>
              <p className="text-sm opacity-80">Check your inbox for a verification email. You can resend it if needed.</p>
            </div>
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  await apiClient.resendVerification();
                  const { toast } = await import('@/components/ui/toast');
                  toast('Verification email sent', 'success');
                } catch (e: any) {
                  const { toast } = await import('@/components/ui/toast');
                  toast(e?.message || 'Failed to resend verification email', 'error');
                }
              }}
            >
              Resend verification email
            </Button>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-semibold text-foreground">Manage Resources</h1>
            {hasProcessingDeployments && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <div className="animate-pulse h-2 w-2 rounded-full bg-blue-500"></div>
                <span>Auto-refreshing</span>
              </div>
            )}
          </div>
          <p className="text-sm text-muted-foreground mt-1">
            Create secure proxies to your private applications
          </p>
        </div>
        <Link to="/create-deployment">
          <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
            + Add Deployment
          </Button>
        </Link>
      </div>

      {/* Search and Filter Inputs */}
      <div className="flex gap-4">
        <Input
          type="text"
          placeholder="Search resources..."
          className="max-w-sm"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        {user?.is_admin && (
          <div className="flex gap-2 items-center">
            <Input
              type="text"
              placeholder="Filter by username or email..."
              className="max-w-sm"
              value={userFilter}
              onChange={(e) => handleUserFilterChange(e.target.value)}
            />
            {userFilter && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearUserFilter}
                className="px-3"
              >
                Clear
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Filter Status Indicator */}
      {userFilter && user?.is_admin && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-blue-900">
                Filtered by user: "{userFilter}"
              </span>
              <span className="text-xs text-blue-700">
                ({filteredDeployments.length} of {deployments.length} deployments)
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearUserFilter}
              className="text-blue-700 hover:text-blue-900 hover:bg-blue-100"
            >
              Clear filter
            </Button>
          </div>
        </div>
      )}

      {isLoading && deployments.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )}

      {!isLoading && filteredDeployments.length === 0 && deployments.length === 0 ? (
        <div className="border rounded-lg p-12 text-center bg-card">
          <h3 className="text-lg font-medium text-foreground mb-2">No deployments yet</h3>
          <p className="text-sm text-muted-foreground mb-6">
            Get started by creating your first deployment
          </p>
          <Link to="/create-deployment">
            <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
              Create Your First Deployment
            </Button>
          </Link>
        </div>
      ) : !isLoading && filteredDeployments.length === 0 && deployments.length > 0 ? (
        <div className="border rounded-lg p-12 text-center bg-card">
          <h3 className="text-lg font-medium text-foreground mb-2">No results found</h3>
          <p className="text-sm text-muted-foreground mb-6">
            {userFilter ?
              `No deployments found for user "${userFilter}". This user may not have any deployments or the username/email may be incorrect.` :
              'Try adjusting your search terms'
            }
          </p>
        </div>
      ) : (
        <>
          <div className="border rounded-lg bg-card ">
            <Table>
              <TableHeader>
                <TableRow className="border-b hover:bg-transparent">
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Name</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Package</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Cloud Provider</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Access</TableHead>
                  {user?.is_admin && (
                    <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Owner</TableHead>
                  )}
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Status</TableHead>
                  <TableHead className="h-12 px-4 text-left text-sm font-medium text-muted-foreground">Created</TableHead>
                  <TableHead className="h-12 px-4">
                    <span className="sr-only">Actions</span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedDeployments.map((deployment) => (
                  <TableRow key={deployment.id} className="border-b hover:bg-muted/50">
                    <TableCell className="h-12 px-4 py-3 text-sm font-medium text-foreground">
                      {deployment.client_name || `deployment-${deployment.id}`}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-foreground">
                      <span className="capitalize">{deployment.package || 'Pangolin'}</span>
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-foreground">
                      {deployment.cloud_provider || 'BYOVPS'}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm">
                      {(() => {
                        const accessUrl = getAccessUrl(deployment);
                        return accessUrl ? (
                          <a
                            href={accessUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline text-sm flex items-center gap-1"
                          >
                            {accessUrl.replace('https://', '')}
                            <ExternalLink className="h-3 w-3" data-testid="external-link-icon" />
                          </a>
                        ) : (
                          <span className="text-muted-foreground">-</span>
                        );
                      })()}
                    </TableCell>
                    {user?.is_admin && (
                      <TableCell className="h-12 px-4 py-3 text-sm text-muted-foreground">
                        {deployment.user?.username || `User ID: ${deployment.user_id}`}
                      </TableCell>
                    )}
                    <TableCell className="h-12 px-4 py-3 text-sm">
                      <div className="flex items-center gap-2">
                        <div className={`h-2 w-2 rounded-full ${
                          deployment.deleted_at ? 'bg-muted-foreground' :
                          deployment.status === 'ACTIVE' ? 'bg-green-500' :
                          deployment.status === 'CREATING' || deployment.status === 'PROVISIONING' ? 'bg-yellow-500' :
                          deployment.status === 'FAILED' ? 'bg-red-500' : 'bg-muted-foreground'
                        }`}></div>
                        <span className={`text-sm font-medium ${
                          deployment.deleted_at ? 'text-muted-foreground' :
                          deployment.status === 'ACTIVE' ? 'text-green-600 dark:text-green-400' :
                          deployment.status === 'CREATING' || deployment.status === 'PROVISIONING' ? 'text-yellow-600 dark:text-yellow-400' :
                          deployment.status === 'FAILED' ? 'text-red-600 dark:text-red-400' : 'text-muted-foreground'
                        }`}>
                          {getStatusText(deployment)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3 text-sm text-muted-foreground">
                      {new Date(deployment.created_at).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="h-12 px-4 py-3">
                      <div className="flex items-center justify-end gap-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {['CREATING', 'PROVISIONING', 'DNS_VALIDATION', 'RESTARTING', 'DESTROYING'].includes(deployment.status) ? (
                              <DropdownMenuItem disabled className="text-muted-foreground">
                                {deployment.status === 'DESTROYING' ? 'Cannot modify during deactivation' : 'Cannot modify during provisioning'}
                              </DropdownMenuItem>
                            ) : deployment.status === 'FAILED' ? (
                              <DropdownMenuItem
                                onClick={() =>
                                  handleDeleteDeployment(deployment)
                                }
                                disabled={deletingId === deployment.id}
                                className="text-destructive focus:text-destructive"
                              >
                                {deletingId === deployment.id
                                  ? 'Deleting...'
                                  : 'Delete'}
                              </DropdownMenuItem>
                            ) : deployment.deleted_at ? (
                              // Disabled/deactivated deployment
                              <DropdownMenuItem
                                onClick={() =>
                                  handleDeleteDeployment(deployment)
                                }
                                disabled={deletingId === deployment.id}
                                className="text-destructive focus:text-destructive"
                              >
                                {deletingId === deployment.id
                                  ? 'Deleting...'
                                  : 'Delete'}
                              </DropdownMenuItem>
                            ) : deployment.status === 'DNS_FAILED' ? (
                              <>
                                <DropdownMenuItem
                                  onClick={() => handleRetryDnsValidation(deployment)}
                                  disabled={deletingId === deployment.id}
                                  className="text-blue-600 focus:text-blue-600"
                                >
                                  Retry DNS Validation
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteDeployment(deployment)
                                  }
                                  disabled={deletingId === deployment.id}
                                  className="text-destructive focus:text-destructive"
                                >
                                  {deletingId === deployment.id
                                    ? 'Deactivating...'
                                    : 'Deactivate'}
                                </DropdownMenuItem>
                              </>
                            ) : deployment.status === 'ACTIVE' || deployment.status === 'READY' ? (
                              <>
                                <DropdownMenuItem
                                  onClick={() => handleRestartDeployment(deployment)}
                                  disabled={deletingId === deployment.id}
                                  className="text-orange-600 focus:text-orange-600"
                                >
                                  Restart Stack (Fix SSL)
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteDeployment(deployment)
                                  }
                                  disabled={deletingId === deployment.id}
                                  className="text-destructive focus:text-destructive"
                                >
                                  {deletingId === deployment.id
                                    ? 'Deactivating...'
                                    : 'Deactivate'}
                                </DropdownMenuItem>
                              </>
                            ) : null}
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="text-sm h-8"
                          onClick={() => {
                            setDetailOpen(deployment.id);
                            setFailureReasonExpanded(false);
                            setCopiedText(null);
                          }}
                        >
                          Details
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {/* Pagination */}
          {filteredDeployments.length > 0 && (
            <div className="flex items-center justify-between text-sm text-muted-foreground mb-20">
              <div className="flex items-center gap-2">
                <select
                  className="h-8 rounded-md border bg-background px-3 py-1 text-sm"
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                >
                  <option value="20">20</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </div>
              <div className="flex items-center gap-4">
                <span>Page {currentPage} of {totalPages}</span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M11 17l-5-5 5-5" />
                      <path d="M5 17l5-5-5-5" />
                    </svg>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(currentPage - 1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M15 18l-6-6 6-6" />
                    </svg>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(currentPage + 1)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M9 18l6-6-6-6" />
                    </svg>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(totalPages)}
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M13 17l5-5-5-5" />
                      <path d="M19 17l-5-5 5-5" />
                    </svg>
                  </Button>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {detailOpen && (
        <Modal
          open={true}
          onClose={() => {
            setDetailOpen(null);
            setFailureReasonExpanded(false);
            setCopiedText(null);
          }}
          title={(() => {
            const d = deployments.find(x => x.id === detailOpen);
            return `${d?.client_name || `Deployment ${detailOpen}`} Details`;
          })()}
        >
          {(() => {
            const d = deployments.find(x => x.id === detailOpen);
            if (!d) return null;            
            return (
              <div className="space-y-6">
                {/* Deployment Status Banner */}
                {(d.status === 'CREATING' || d.status === 'PROVISIONING') && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mt-0.5"></div>
                      <div>
                        <h4 className="text-sm font-medium text-blue-900 mb-1">
                          Deployment in Progress
                        </h4>
                        <p className="text-sm text-blue-700">
                          Your deployment may take up to 5 minutes to complete. Please wait while we provision your infrastructure. You may close this window.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* DNS Success Banner */}
                {d.domain && (d.status === 'READY' || d.status === 'ACTIVE') && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="text-green-600 mt-0.5">
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-green-900 mb-1">
                          Deployment Ready
                        </h4>
                        <p className="text-sm text-green-700">
                          Your deployment is operational and, when ready, should be accessible at <strong>{d.admin_subdomain}.{d.domain}</strong>. If not, allow up to 15 mins for the SSL certs to propagate. You can restart the deployment to fix SSL issues. Please be patient!
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* DNS Setup Banner */}
                {d.domain && ['CREATING', 'PROVISIONING', 'DNS_VALIDATION', 'DNS_FAILED'].includes(d.status) && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="text-amber-600 mt-0.5">
                        <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-amber-900 mb-1">
                          DNS Configuration Required
                        </h4>
                        <p className="text-sm text-amber-700">
                          {d.status === 'DNS_FAILED' ? (
                            <>DNS validation failed. Please point your DNS entry for <strong>{d.domain}</strong> to the IP address shown above using an A-Record (e.g., * → IP address), then click "Retry DNS Validation".</>
                          ) : (
                            <>Once your deployment is active, you'll need to point your DNS entry for <strong>{d.domain}</strong> to the assigned IP address using an A-Record (e.g., * → IP address).</>
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-foreground mb-3">General Information</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Name</span>
                        <span className="text-sm font-medium">{d.client_name || `deployment-${d.id}`}</span>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Package</span>
                        <span className="text-sm font-medium capitalize">{d.package || 'Pangolin'}</span>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Status</span>
                        <div className="flex items-center gap-2">
                          <div className={`h-2 w-2 rounded-full ${
                            d.deleted_at ? 'bg-gray-400' :
                            d.status === 'READY' ? 'bg-green-500' :
                            d.status === 'ACTIVE' || d.status === 'DNS_VALIDATION' ? 'bg-blue-500' :
                            d.status === 'CREATING' || d.status === 'PROVISIONING' ? 'bg-yellow-500' :
                            d.status === 'DNS_FAILED' ? 'bg-orange-500' :
                            d.status === 'FAILED' ? 'bg-red-500' : 'bg-gray-400'
                          }`}></div>
                          <span className={`text-sm font-medium ${
                            d.deleted_at ? 'text-gray-500' :
                            d.status === 'READY' ? 'text-green-600' :
                            d.status === 'ACTIVE' || d.status === 'DNS_VALIDATION' ? 'text-blue-600' :
                            d.status === 'CREATING' || d.status === 'PROVISIONING' ? 'text-yellow-600' :
                            d.status === 'DNS_FAILED' ? 'text-orange-600' :
                            d.status === 'FAILED' ? 'text-red-600' : 'text-gray-500'
                          }`}>
                            {getStatusText(d)}
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between py-1">
                        <span className="text-sm text-muted-foreground">Created</span>
                        <span className="text-sm font-medium">{new Date(d.created_at).toLocaleString()}</span>
                      </div>
                      {d.domain && (
                        <div className="flex justify-between py-1">
                          <span className="text-sm text-muted-foreground">Domain</span>
                          <span className="text-sm font-medium">{d.domain}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Failure Reason Section */}
                  {d.status === 'FAILED' && d.failure_reason && (
                    <div>
                      <h4 className="text-sm font-medium text-foreground mb-3">Failure Details</h4>
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <div className="flex items-start gap-3">
                          <div className="text-red-600 mt-0.5">
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-red-900 mb-2">
                              Deployment Failed
                            </h4>
                            <button
                              onClick={() => setFailureReasonExpanded(!failureReasonExpanded)}
                              className="text-sm text-red-700 hover:text-red-800 underline focus:outline-none"
                            >
                              {failureReasonExpanded ? 'Hide' : 'Show'} failure details
                            </button>
                            {failureReasonExpanded && (
                              <div className="mt-3 p-3 bg-red-100 rounded border">
                                <div className="flex items-start justify-between gap-2">
                                  <pre className="text-xs text-red-800 whitespace-pre-wrap font-mono flex-1">
                                    {d.failure_reason}
                                  </pre>
                                  <button
                                    onClick={() => copyToClipboard(d.failure_reason || '', 'failure-reason')}
                                    className="flex-shrink-0 p-1 text-red-600 hover:text-red-800 hover:bg-red-200 rounded transition-colors"
                                    title="Copy failure details"
                                  >
                                    {copiedText === 'failure-reason' ? (
                                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    ) : (
                                      <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                        <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                      </svg>
                                    )}
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {(() => {
                    // Show Infrastructure section if we have cloud provider info or if it's BYOVPS
                    const isByovps = d.server_type === 'vps' || (!d.cloud_provider && d.vps_ip_address);
                    const showInfrastructure = d.cloud_provider || isByovps;

                    if (!showInfrastructure) return null;

                    return (
                      <div>
                        <h4 className="text-sm font-medium text-foreground mb-3">Infrastructure</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between py-1">
                            <span className="text-sm text-muted-foreground">Cloud Provider</span>
                            <span className="text-sm font-medium">
                              {isByovps ? 'BYOVPS' : d.cloud_provider}
                            </span>
                          </div>
                          {/* Only show region and instance type for non-BYOVPS deployments */}
                          {!isByovps && d.region && (
                            <div className="flex justify-between py-1">
                              <span className="text-sm text-muted-foreground">Region</span>
                              <span className="text-sm font-medium">{d.region}</span>
                            </div>
                          )}
                          {!isByovps && d.instance_type && (
                            <div className="flex justify-between py-1">
                              <span className="text-sm text-muted-foreground">Instance Type</span>
                              <span className="text-sm font-medium">{d.instance_type}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                  
                  <div>
                    <h4 className="text-sm font-medium text-foreground mb-3">Network</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center py-1">
                        <span className="text-sm text-muted-foreground">IP Address</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium font-mono">
                            {(() => {
                              // Debug: Log the IP fields to console
                              console.log(`Deployment ${d.id} IP fields:`, {
                                vps_ip_address: d.vps_ip_address,
                                instance_ip: d.instance_ip,
                                komodo_host_ip: d.komodo_host_ip,
                                status: d.status
                              });

                              // Check all possible IP fields (prioritize based on deployment type)
                              // For BYOVPS: vps_ip_address is primary
                              // For cloud: instance_ip is primary
                              // komodo_host_ip is internal only
                              const ip = d.vps_ip_address || d.instance_ip;

                              console.log(`Deployment ${d.id} selected IP:`, ip);

                              // Always show IP if available, regardless of status
                              if (ip && ip.trim() !== '' && ip !== 'null' && ip !== 'undefined') {
                                console.log(`Deployment ${d.id} showing IP:`, ip);
                                return ip;
                              }

                              console.log(`Deployment ${d.id} no IP found, showing status message`);
                              // Only show status messages when no IP is available
                              switch (d.status) {
                                case 'CREATING':
                                  return 'Creating...';
                                case 'PROVISIONING':
                                  return 'Provisioning...';
                                case 'FAILED':
                                  return 'Failed';
                                default:
                                  return 'No IP assigned yet';
                              }
                            })()}
                          </span>
                          {(() => {
                            const ip = d.vps_ip_address || d.instance_ip;
                            if (ip && ip.trim() !== '' && ip !== 'null' && ip !== 'undefined') {
                              return (
                                <button
                                  onClick={() => copyToClipboard(ip, 'ip-address')}
                                  className="p-1 text-muted-foreground hover:text-foreground hover:bg-muted rounded transition-colors"
                                  title="Copy IP address"
                                >
                                  {copiedText === 'ip-address' ? (
                                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  ) : (
                                    <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                      <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                      <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                                    </svg>
                                  )}
                                </button>
                              );
                            }
                            return null;
                          })()}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>


              </div>
            );
          })()}
        </Modal>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={(() => {
          if (deploymentToDelete?.status === 'FAILED') return 'Delete Failed Deployment';
          if (deploymentToDelete?.deleted_at) return 'Delete Deployment';
          return 'Deactivate Deployment';
        })()}
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            {deploymentToDelete?.status === 'FAILED' ? (
              <>
                Are you sure you want to delete the failed deployment "{deploymentToDelete?.client_name || `deployment-${deploymentToDelete?.id}`}"?
                <br /><br />
                This will permanently remove the deployment from your account and clean up any remaining files. Since the deployment failed, no billing charges apply.
              </>
            ) : deploymentToDelete?.deleted_at ? (
              <>
                Are you sure you want to permanently delete the deployment "{deploymentToDelete?.client_name || `deployment-${deploymentToDelete?.id}`}"?
                <br /><br />
                This will permanently remove the deployment from your account. This action cannot be undone.
              </>
            ) : (
              <>
                Are you sure you want to deactivate the deployment "{deploymentToDelete?.client_name || `deployment-${deploymentToDelete?.id}`}"?
                <br /><br />
                This will stop the deployment and destroy the associated infrastructure. You can delete it permanently later if needed.
              </>
            )}
          </p>
          {deploymentToDelete?.status === 'ACTIVE' && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-700 font-medium">
                ⚠️ Warning: This will destroy all associated infrastructure. Billing will stop after deactivation.
              </p>
            </div>
          )}
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteDeployment}
              disabled={deletingId === deploymentToDelete?.id}
            >
              {deletingId === deploymentToDelete?.id ?
                (deploymentToDelete?.status === 'FAILED' || deploymentToDelete?.deleted_at ? 'Deleting...' : 'Deactivating...') :
                (deploymentToDelete?.status === 'FAILED' || deploymentToDelete?.deleted_at ? 'Delete' : 'Deactivate')
              }
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Dashboard;
